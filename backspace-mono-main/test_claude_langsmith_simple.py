"""Simple test for Claude CLI stream tracing with Lang<PERSON>mith"""

import asyncio
import json
import os
import subprocess
from datetime import datetime
from lang<PERSON> import traceable, Client, get_current_run_tree

# Set up LangSmith environment variables
os.environ["LANGSMITH_TRACING"] = "true"
os.environ["LANGSMITH_PROJECT"] = "claude-stream-test"

@traceable(
    name="Claude CLI Stream",
    run_type="chain",
    metadata={"execution_type": "claude_cli", "stream_based": True},
    tags=["claude-cli", "streaming"]
)
async def run_claude_with_tracing(prompt: str):
    """Run Claude CLI and trace each streamed event to LangSmith"""
    
    print("🚀 Starting Claude CLI stream tracing")
    print(f"📊 LangSmith Project: {os.getenv('LANGSMITH_PROJECT')}")
    
    # Build command
    cmd = [
        "claude", "-p", prompt,
        "--output-format", "stream-json",
        "--verbose",
        "--dangerously-skip-permissions"
    ]
    
    print(f"\n📝 Running command: {' '.join(cmd)}")
    print("="*60)
    
    # Get parent run for creating child traces
    parent_run = get_current_run_tree()
    
    # Start process
    process = await asyncio.create_subprocess_exec(
        *cmd,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE
    )
    
    # Track statistics
    event_counts = {}
    
    # Process stdout stream
    async for line in process.stdout:
        try:
            # Parse JSON event
            event = json.loads(line.decode())
            event_type = event.get('type', 'unknown')
            event_counts[event_type] = event_counts.get(event_type, 0) + 1
            
            # Create child trace for this event
            if parent_run:
                # Determine trace name and type based on event
                if event_type == "system":
                    trace_name = "System Init"
                    run_type = "chain"
                    content = f"Model: {event.get('model', 'unknown')}"
                elif event_type == "assistant":
                    msg = event.get('message', {})
                    content_items = msg.get('content', [])
                    for item in content_items:
                        if item.get('type') == 'text':
                            trace_name = "Claude Message"
                            run_type = "llm"
                            content = item.get('text', '')[:100] + "..."
                        elif item.get('type') == 'tool_use':
                            trace_name = f"Tool Call: {item.get('name', 'unknown')}"
                            run_type = "tool"
                            content = item.get('input', {})
                elif event_type == "user":
                    trace_name = "User/Tool Result"
                    run_type = "chain"
                    content = event
                elif event_type == "result":
                    trace_name = "Final Result"
                    run_type = "chain"
                    content = f"Success: {not event.get('is_error', False)}"
                else:
                    trace_name = f"Event: {event_type}"
                    run_type = "chain"
                    content = event
                
                # Create child run
                child_run = parent_run.create_child(
                    name=trace_name,
                    run_type=run_type,
                    inputs={"event_type": event_type},
                    metadata={"timestamp": datetime.now().isoformat()}
                )
                
                # Log event details
                child_run.on_chain_start({"raw_event": event})
                child_run.on_chain_end({"content": content})
                child_run.end()
                
                # Print progress
                print(f"📍 {trace_name}")
                
        except json.JSONDecodeError:
            print(f"⚠️  Failed to parse: {line}")
        except Exception as e:
            print(f"❌ Error processing event: {e}")
    
    # Wait for process to complete
    return_code = await process.wait()
    
    print("\n" + "="*60)
    print("📊 EXECUTION SUMMARY:")
    print(f"   ✅ Exit code: {return_code}")
    print(f"   📈 Event counts:")
    for event_type, count in event_counts.items():
        print(f"      - {event_type}: {count}")
    
    # Add summary to parent trace
    if parent_run:
        parent_run.end(outputs={
            "event_counts": event_counts,
            "exit_code": return_code
        })
    
    return return_code == 0

async def main():
    """Main test function"""
    
    # Check for API key
    if not os.getenv('LANGSMITH_API_KEY'):
        print("⚠️  LANGSMITH_API_KEY not set!")
        print("To trace to LangSmith, run:")
        print("   export LANGSMITH_API_KEY=your-key-here")
        print("\nContinuing with local-only demonstration...\n")
    
    # Simple test prompt
    prompt = "List the current directory files"
    
    success = await run_claude_with_tracing(prompt)
    
    if success:
        print("\n✅ Test completed successfully!")
    else:
        print("\n❌ Test failed!")
    
    # Show LangSmith link if API key is set
    if os.getenv('LANGSMITH_API_KEY'):
        print(f"\n🔗 View trace in LangSmith:")
        print(f"   https://smith.langchain.com/o/YOUR-ORG-ID/projects/p/{os.getenv('LANGSMITH_PROJECT')}")

if __name__ == "__main__":
    asyncio.run(main()) 