"""Test Claude CLI stream tracing with <PERSON><PERSON><PERSON>"""

import asyncio
import json
import os
from datetime import datetime
from e2b_code_interpreter import AsyncSandbox

# Import the enhanced <PERSON> functions
from src.agents.claude_e2b.claude import run_claude_in_sandbox, ClaudeOutput

# Set up LangSmith environment variables
os.environ["LANGSMITH_TRACING"] = "true"
os.environ["LANGSMITH_PROJECT"] = "claude-stream-test"
# Note: You'll need to set LANGSMITH_API_KEY separately

async def test_claude_streaming():
    """Test Claude streaming with LangSmith tracing"""
    
    print("🚀 Starting Claude CLI stream tracing test")
    print(f"📊 LangSmith Project: {os.getenv('LANGSMITH_PROJECT')}")
    print(f"🔑 API Key Set: {'Yes' if os.getenv('LANGSMITH_API_KEY') else 'No'}")
    
    if not os.getenv('LANGSMITH_API_KEY'):
        print("\n⚠️  Please set LANGSMITH_API_KEY environment variable!")
        print("   export LANGSMITH_API_KEY=your-key-here")
        return
    
    # Create sandbox
    print("\n🏗️  Creating E2B sandbox...")
    sandbox = await AsyncSandbox.create()
    print(f"✅ Sandbox created: {sandbox.sandbox_id}")
    
    try:
        # Track outputs for verification
        outputs_collected = []
        
        async def handle_output(output: ClaudeOutput):
            """Callback to handle each output"""
            outputs_collected.append(output)
            
            # Print a summary of each event
            if output.type == "system":
                print(f"\n🚀 SYSTEM: {output.content}")
            elif output.type == "claude_message":
                print(f"\n🤖 CLAUDE: {output.content[:100]}...")
            elif output.type == "tool_call":
                print(f"\n🔧 TOOL CALL: {output.content.get('name')} - {output.content.get('input', {})}")
            elif output.type == "tool_result":
                print(f"\n📋 TOOL RESULT: {output.content.get('content', '')[:100]}...")
            elif output.type == "result":
                print(f"\n✅ FINAL RESULT: Success={not output.content.get('is_error')}")
        
        # Run Claude with a simple command that will use tools
        prompt = "List the files in the current directory and count how many Python files there are"
        
        print(f"\n📝 Prompt: {prompt}")
        print("\n" + "="*60)
        print("🎬 Starting Claude CLI execution...")
        print("="*60)
        
        # Execute with streaming and tracing
        session = await run_claude_in_sandbox(
            sandbox=sandbox,
            prompt=prompt,
            claude_options={"max-turns": "5"},
            on_output=handle_output,
            timeout=60
        )
        
        print("\n" + "="*60)
        print("📊 EXECUTION SUMMARY:")
        print(f"   🆔 Session ID: {session.session_id}")
        print(f"   ⏱️  Duration: {session.elapsed_time:.2f}s")
        print(f"   📝 Total outputs: {len(session.outputs)}")
        print(f"   ✅ Success: {session.success}")
        if session.error:
            print(f"   ❌ Error: {session.error}")
        
        # Show breakdown of output types
        output_types = {}
        for output in session.outputs:
            output_types[output.type] = output_types.get(output.type, 0) + 1
        
        print("\n📈 Output breakdown:")
        for output_type, count in output_types.items():
            print(f"   - {output_type}: {count}")
        
        # Show LangSmith link
        print(f"\n🔗 View trace in LangSmith:")
        print(f"   https://smith.langchain.com/public/{os.getenv('LANGSMITH_PROJECT')}/r/{session.session_id}")
        print("   (Note: Update URL with your org ID)")
        
    finally:
        # Cleanup
        print("\n🧹 Cleaning up sandbox...")
        await sandbox.close()
        print("✅ Done!")

if __name__ == "__main__":
    # Check if running from correct directory
    if not os.path.exists("src/agents/claude_e2b/claude.py"):
        print("❌ Please run this script from the apps/agent directory!")
        exit(1)
    
    asyncio.run(test_claude_streaming()) 