#!/usr/bin/env python3
"""
Test Claude CLI stream tracing with LangSmith
This demonstrates how each Claude stream event is traced as a child span in LangSmith
"""

import asyncio
import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Configure LangSmith
os.environ["LANGSMITH_TRACING"] = "true"
os.environ["LANGSMITH_PROJECT"] = "claude-stream-tracing"

print("🚀 Claude CLI Stream Tracing with LangSmith")
print("=" * 60)

async def main():
    """Main test function"""
    
    # Check environment
    print("📊 Environment Check:")
    print(f"   LANGSMITH_PROJECT: {os.getenv('LANGSMITH_PROJECT')}")
    print(f"   LANGSMITH_API_KEY: {'✅ Set' if os.getenv('LANGSMITH_API_KEY') else '❌ Not set'}")
    print(f"   E2B_API_KEY: {'✅ Set' if os.getenv('E2B_API_KEY') else '❌ Not set'}")
    
    if not os.getenv('E2B_API_KEY'):
        print("\n⚠️  E2B_API_KEY not set. Using mock demo mode...")
        print("\n📝 What this would trace to LangSmith:")
        print("   📍 Claude Code CLI Execution (root)")
        print("      ├── 🚀 System Init")
        print("      │   └─ Model: claude-3-opus-20240229")
        print("      ├── 🤖 Claude Message")
        print("      │   └─ 'I'll list the files in the directory'")
        print("      ├── 🔧 Tool Use: LS")
        print("      │   └─ Input: {'path': '/home/<USER>/workspace'}")
        print("      ├── 📋 Tool Result")
        print("      │   └─ '- file1.py\\n- file2.py\\n- README.md'")
        print("      ├── 🤖 Claude Message")
        print("      │   └─ 'I found 2 Python files...'")
        print("      └── ✅ Final Result")
        print("          └─ Success: true")
        print("\n💡 With E2B_API_KEY set, you'd see this live in LangSmith!")
        return
    
    # Import the enhanced functions
    from agents.claude_e2b.e2b_sandbox import create_sandbox, cleanup_sandbox
    from agents.claude_e2b.claude import run_claude_in_sandbox, ClaudeOutput
    
    print("\n🏗️  Creating E2B sandbox...")
    sandbox = await create_sandbox()
    print(f"✅ Sandbox created: {sandbox.sandbox_id}")
    
    try:
        # Simple test prompt
        prompt = "List the Python files in the current directory"
        
        print(f"\n📝 Prompt: {prompt}")
        print("\n" + "="*60)
        print("🎬 EXECUTING CLAUDE CLI WITH LANGSMITH TRACING")
        print("="*60)
        
        # Track events
        event_count = {"system": 0, "claude_message": 0, "tool_call": 0, "tool_result": 0, "result": 0}
        
        async def on_output(output: ClaudeOutput):
            """Process each output event"""
            event_count[output.type] = event_count.get(output.type, 0) + 1
            
            # Visual feedback
            if output.type == "system":
                print(f"   📍 System Init → LangSmith")
            elif output.type == "claude_message":
                print(f"   📍 Claude Message #{event_count['claude_message']} → LangSmith")
            elif output.type == "tool_call":
                tool_name = output.content.get('name', 'unknown') if isinstance(output.content, dict) else 'unknown'
                print(f"   📍 Tool Use: {tool_name} → LangSmith")
            elif output.type == "tool_result":
                print(f"   📍 Tool Result → LangSmith")
            elif output.type == "result":
                print(f"   📍 Final Result → LangSmith")
        
        # Execute with tracing
        session = await run_claude_in_sandbox(
            sandbox=sandbox,
            prompt=prompt,
            claude_options={"max-turns": "3"},
            on_output=on_output,
            timeout=30
        )
        
        print("\n" + "="*60)
        print("✅ EXECUTION COMPLETE")
        print("="*60)
        
        print(f"\n📊 Trace Summary:")
        print(f"   Session ID: {session.session_id}")
        print(f"   Duration: {session.elapsed_time:.2f}s")
        print(f"   Events traced to LangSmith: {sum(event_count.values())}")
        
        print(f"\n📈 Event Breakdown (all traced as child spans):")
        for event_type, count in event_count.items():
            if count > 0:
                print(f"   {event_type}: {count}")
        
        print(f"\n🔗 View your traces in LangSmith:")
        print(f"   https://smith.langchain.com/o/YOUR-ORG-ID/projects/p/{os.getenv('LANGSMITH_PROJECT')}")
        print(f"   Look for: 'Claude Code CLI Execution'")
        print(f"   Session: {session.session_id}")
        
    finally:
        print("\n🧹 Cleaning up...")
        await cleanup_sandbox(sandbox)
        print("✅ Done!")
    
    print("\n🎉 Each Claude stream event was traced to LangSmith!")
    print("   Check the trace tree to see the full execution flow.")

if __name__ == "__main__":
    asyncio.run(main()) 