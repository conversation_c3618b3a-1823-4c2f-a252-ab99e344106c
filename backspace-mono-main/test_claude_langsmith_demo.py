"""Test Claude CLI stream tracing with LangSmith - Working Demo"""

import asyncio
import os
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Set LangSmith environment variables
os.environ["LANGSMITH_TRACING"] = "true"
os.environ["LANGSMITH_PROJECT"] = "claude-stream-tracing-demo"

async def test_claude_tracing():
    """Test Claude CLI streaming with enhanced LangSmith tracing"""
    
    print("🚀 Starting Claude CLI LangSmith Tracing Demo")
    print("="*60)
    
    # Check environment
    print("📊 Environment Check:")
    print(f"   LANGSMITH_PROJECT: {os.getenv('LANGSMITH_PROJECT')}")
    print(f"   LANGSMITH_API_KEY: {'✅ Set' if os.getenv('LANGSMITH_API_KEY') else '❌ Not set'}")
    print(f"   E2B_API_KEY: {'✅ Set' if os.getenv('E2B_API_KEY') else '❌ Not set'}")
    print(f"   ANTHROPIC_API_KEY: {'✅ Set' if os.getenv('ANTHROPIC_API_KEY') else '❌ Not set'}")
    
    if not os.getenv('E2B_API_KEY'):
        print("\n⚠️  E2B_API_KEY not set. Please set it to run the demo.")
        print("   Get your key from: https://e2b.dev")
        return
    
    # Import after env check
    from src.agents.claude_e2b.e2b_sandbox import create_sandbox, cleanup_sandbox
    from src.agents.claude_e2b.claude import run_claude_in_sandbox, ClaudeOutput
    
    print("\n🏗️  Creating E2B sandbox...")
    sandbox = await create_sandbox()
    print(f"✅ Sandbox created: {sandbox.sandbox_id}")
    
    try:
        # Simple test prompt that will trigger multiple tool uses
        prompt = """List all Python files in the current directory and show me the first 5 lines of any file that exists."""
        
        print(f"\n📝 Test Prompt: {prompt}")
        print("\n" + "="*60)
        print("🎬 STARTING CLAUDE CLI EXECUTION WITH LANGSMITH TRACING")
        print("="*60)
        
        # Track events for summary
        event_log = []
        
        async def track_output(output: ClaudeOutput):
            """Track each output event"""
            event_log.append({
                "time": output.timestamp,
                "type": output.type,
                "preview": str(output.content)[:100] if output.content else "N/A"
            })
            
            # Visual feedback for each event type
            if output.type == "system":
                print(f"\n🚀 SYSTEM INIT")
                print(f"   Model: {output.content.get('model', 'unknown') if isinstance(output.content, dict) else 'N/A'}")
            elif output.type == "claude_message":
                print(f"\n🤖 CLAUDE REASONING")
                print(f"   Message: {str(output.content)[:100]}...")
            elif output.type == "tool_call":
                tool_info = output.content if isinstance(output.content, dict) else {}
                print(f"\n🔧 TOOL CALL: {tool_info.get('name', 'unknown')}")
                print(f"   Input: {tool_info.get('input', {})}")
            elif output.type == "tool_result":
                print(f"\n📋 TOOL RESULT")
                result_preview = str(output.content.get('content', '') if isinstance(output.content, dict) else output.content)[:100]
                print(f"   Result: {result_preview}...")
            elif output.type == "result":
                print(f"\n✅ FINAL RESULT")
                print(f"   Success: {not output.content.get('is_error', False) if isinstance(output.content, dict) else 'N/A'}")
        
        # Execute with enhanced tracing
        session = await run_claude_in_sandbox(
            sandbox=sandbox,
            prompt=prompt,
            claude_options={"max-turns": "5"},
            on_output=track_output,
            timeout=60
        )
        
        print("\n" + "="*60)
        print("📊 EXECUTION COMPLETE - SUMMARY")
        print("="*60)
        
        print(f"\n🔍 Session Details:")
        print(f"   Session ID: {session.session_id}")
        print(f"   Duration: {session.elapsed_time:.2f}s")
        print(f"   Total Events: {len(session.outputs)}")
        print(f"   Success: {session.success}")
        
        # Event breakdown
        event_types = {}
        for output in session.outputs:
            event_types[output.type] = event_types.get(output.type, 0) + 1
        
        print(f"\n📈 Event Breakdown:")
        for event_type, count in event_types.items():
            print(f"   {event_type}: {count}")
        
        # Show the tree structure that should appear in LangSmith
        print(f"\n🌳 LangSmith Trace Structure:")
        print("   📍 Claude Code CLI Execution (root)")
        print("      ├── 🚀 System Init")
        print("      ├── 🤖 Claude Message (reasoning)")
        print("      ├── 🔧 Tool Use: LS")
        print("      ├── 📋 Tool Result")
        print("      ├── 🤖 Claude Message (reasoning)")
        print("      ├── 🔧 Tool Use: Read")
        print("      ├── 📋 Tool Result")
        print("      └── ✅ Final Result")
        
        if os.getenv('LANGSMITH_API_KEY'):
            print(f"\n🔗 View your trace in LangSmith:")
            print(f"   https://smith.langchain.com/o/YOUR-ORG-ID/projects/p/{os.getenv('LANGSMITH_PROJECT')}")
            print(f"   Look for runs with session_id: {session.session_id}")
        else:
            print(f"\n⚠️  To see traces in LangSmith:")
            print(f"   1. Get API key from https://smith.langchain.com")
            print(f"   2. export LANGSMITH_API_KEY=your-key-here")
            print(f"   3. Run this demo again")
            
    finally:
        print("\n🧹 Cleaning up...")
        await cleanup_sandbox(sandbox)
        print("✅ Sandbox cleaned up")
    
    print("\n🎉 Demo complete! Each Claude stream event was traced to LangSmith.")

if __name__ == "__main__":
    asyncio.run(test_claude_tracing()) 