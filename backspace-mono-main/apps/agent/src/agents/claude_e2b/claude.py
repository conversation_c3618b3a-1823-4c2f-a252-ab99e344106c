"""Claude Code integration utilities for E2B sandbox."""

import json
import logging
import asyncio
from typing import Optional, List, Dict, Any, AsyncIterator, Callable
from dataclasses import dataclass, field
from datetime import datetime

from e2b_code_interpreter import AsyncSandbox

# Add LangSmith observability imports
from langsmith import traceable, get_current_run_tree

# Configure logger with emoji support
logger = logging.getLogger(__name__)


@dataclass
class ClaudeOutput:
    """Container for storing Claude Code outputs."""
    timestamp: float
    type: str
    content: Any
    raw_event: Optional[Dict[str, Any]] = None
    
    def __str__(self):
        return f"[{self.timestamp:.2f}] {self.type}: {self.content}"


@dataclass
class ClaudeSession:
    """Container for a complete Claude Code session."""
    session_id: str
    prompt: str
    outputs: List[ClaudeOutput] = field(default_factory=list)
    start_time: float = field(default_factory=lambda: datetime.now().timestamp())
    end_time: Optional[float] = None
    total_cost_usd: Optional[float] = None
    duration_ms: Optional[int] = None
    success: bool = False
    error: Optional[str] = None
    
    def add_output(self, output: ClaudeOutput):
        """Add an output to the session."""
        self.outputs.append(output)
    
    def finalize(self, success: bool = True, error: Optional[str] = None):
        """Mark the session as complete."""
        self.end_time = datetime.now().timestamp()
        self.success = success
        self.error = error
        
    @property
    def elapsed_time(self) -> float:
        """Get elapsed time in seconds."""
        if self.end_time:
            return self.end_time - self.start_time
        return datetime.now().timestamp() - self.start_time


@traceable(name="Claude Stream Event", run_type="chain")
def _trace_claude_stream_event(output: ClaudeOutput) -> ClaudeOutput:
    """Create a nested LangSmith trace for a single Claude stream event."""
    if not output:
        return output

    # Use a more descriptive name for the trace based on the event type
    trace_name_map = {
        "system": "System Info",
        "claude_message": "Claude Reasoning",
        "tool_call": f"Tool Call: {output.content.get('name', 'unknown') if isinstance(output.content, dict) else 'unknown'}",
        "tool_result": "Tool Result",
        "result": "Final Result",
        "error": "Error"
    }
    trace_name = trace_name_map.get(output.type, "Claude Event")

    # Define the run_type for better categorization in LangSmith
    run_type_map = {
        "claude_message": "llm",
        "tool_call": "tool",
        "tool_result": "tool",
    }
    run_type = run_type_map.get(output.type, "chain")

    try:
        current_run = get_current_run_tree()
        if current_run:
            current_run.name = trace_name
            current_run.run_type = run_type
            
            # Add relevant inputs and outputs for clarity
            if output.type == "claude_message":
                current_run.inputs = {"type": "claude_message"}
                current_run.outputs = {"response": output.content}
            elif output.type == "tool_call":
                current_run.inputs = output.content.get("input", {}) if isinstance(output.content, dict) else {}
                current_run.outputs = {"tool_name": output.content.get("name", "unknown") if isinstance(output.content, dict) else "unknown"}
            elif output.type == "tool_result":
                current_run.inputs = {"tool_use_id": output.content.get("tool_use_id") if isinstance(output.content, dict) else None}
                current_run.outputs = {"result": output.content.get("content", "") if isinstance(output.content, dict) else str(output.content)}
            
            # Add the full raw event as metadata
            current_run.add_metadata({"raw_event": output.raw_event, "timestamp": output.timestamp})

    except Exception as e:
        logger.error(f"Failed to create LangSmith trace for event: {e}")
    
    return output


def handle_claude_stream(line: str, session: Optional[ClaudeSession] = None) -> Optional[ClaudeOutput]:
    """Handle a single line from Claude's stream output."""
    # ... (rest of the function remains the same)
    if not line.strip():
        return None

    try:
        event = json.loads(line)
        event_type = event.get('type')
        timestamp = datetime.now().timestamp()
        output = None

        if event_type == 'system':
            logger.info("🚀 SYSTEM INIT")
            # ... (logging remains the same)
            output = ClaudeOutput(
                timestamp=timestamp,
                type="system",
                content={"cwd": event.get('cwd'), "model": event.get('model')},
                raw_event=event
            )

        elif event_type == 'assistant':
            msg = event.get('message', {})
            content = msg.get('content', [])
            for item in content:
                if item.get('type') == 'text':
                    text = item.get('text', '')
                    logger.info(f"🤖 Claude: {text}")
                    output = ClaudeOutput(
                        timestamp=timestamp,
                        type="claude_message",
                        content=text,
                        raw_event=event
                    )
                elif item.get('type') == 'tool_use':
                    tool = item.get('name')
                    inp = item.get('input', {})
                    logger.info(f"🔧 Tool: {tool}")
                    output = ClaudeOutput(
                        timestamp=timestamp,
                        type="tool_call",
                        content={"name": tool, "input": inp},
                        raw_event=event
                    )

        elif event_type == 'user':
            # ... (logic remains the same)
            msg = event.get('message', {})
            for item in msg.get('content', []):
                if item.get('type') == 'tool_result':
                    # ...
                    output = ClaudeOutput(
                        timestamp=timestamp,
                        type="tool_result",
                        content={"tool_use_id": item.get('tool_use_id'), "is_error": item.get('is_error', False), "content": item.get('content', '')},
                        raw_event=event
                    )

        elif event_type == 'result':
            # ... (logic remains the same)
            output = ClaudeOutput(
                timestamp=timestamp,
                type="result",
                content={"is_error": event.get('is_error', False), "result": event.get('result', '')},
                raw_event=event
            )
            if session:
                session.finalize(success=not event.get('is_error', False), error=event.get('result', '') if event.get('is_error', False) else None)

        elif event_type == 'error':
            # ... (logic remains the same)
            error_msg = event.get('error', 'Unknown error')
            logger.error(f"💥 Error: {error_msg}")
            output = ClaudeOutput(
                timestamp=timestamp,
                type="error",
                content=error_msg,
                raw_event=event
            )
            if session:
                session.finalize(success=False, error=error_msg)

        if output and session:
            session.add_output(output)
        
        return output

    except json.JSONDecodeError:
        return None
    except Exception as e:
        logger.error(f"💥 Unexpected error handling stream: {e}")
        return None


@traceable(
    name="Claude Code CLI Execution",
    run_type="llm",
    metadata={"execution_type": "claude_cli", "stream_based": True},
    tags=["claude-cli", "e2b-sandbox", "streaming"]
)
async def run_claude_in_sandbox(
    sandbox: AsyncSandbox,
    prompt: str,
    claude_options: Optional[Dict[str, Any]] = None,
    on_output: Optional[Callable[[ClaudeOutput], None]] = None,
    cwd: Optional[str] = None,
    timeout: int = 300
) -> ClaudeSession:
    """Run Claude Code in the E2B sandbox with full observability."""

    current_run = get_current_run_tree()
    session_id = f"e2b-{sandbox.sandbox_id}-{int(datetime.now().timestamp())}"
    session = ClaudeSession(session_id=session_id, prompt=prompt)

    # Log execution context
    if current_run:
        current_run.add_metadata({
            "session_id": session_id,
            "sandbox_id": sandbox.sandbox_id,
            "prompt_length": len(prompt),
            "prompt_preview": prompt[:200],
            "claude_options": claude_options,
            "timeout": timeout
        })

    logger.info(f"🤖 Starting Claude Code session: {session_id}")
    logger.info(f"📝 Prompt: {prompt[:100]}{'...' if len(prompt) > 100 else ''}")
    
    # Build Claude command
    cmd_parts = ["claude", "-p", "--output-format", "stream-json", "--verbose", "--dangerously-skip-permissions"]
    
    # Add any additional options
    if claude_options:
        for key, value in claude_options.items():
            if key.startswith("--"):
                cmd_parts.append(key)
                if value is not None:
                    cmd_parts.append(str(value))
            else:
                cmd_parts.append(f"--{key}")
                if value is not None:
                    cmd_parts.append(str(value))
    
    # Add the prompt via echo
    # Escape the prompt for shell
    escaped_prompt = prompt.replace("'", "'\"'\"'")
    full_command = f"echo '{escaped_prompt}' | {' '.join(cmd_parts)}"

    # Log the actual command being executed
    if current_run:
        current_run.add_metadata({
            "full_command": full_command,
            "command_parts": cmd_parts
        })

    logger.debug(f"🔧 Full command: {full_command[:200]}...")
    
    try:
        # Run the command with streaming
        logger.info("⚡ Executing Claude Code...")
        
        async def handle_stdout(data: str):
            """Handle stdout data from the command with enhanced LangSmith tracing."""
            for line in data.strip().split('\n'):
                if line:
                    output = handle_claude_stream(line, session)
                    if output:
                        # Create nested trace for each event
                        traced_output = _trace_claude_stream_event(output)
                        
                        # Additionally create proper child traces for better visibility
                        if current_run:
                            # Create more detailed child runs based on event type
                            child_metadata = {
                                "timestamp": output.timestamp,
                                "session_id": session_id,
                                "event_type": output.type
                            }
                            
                            if output.type == "system":
                                child = current_run.create_child(
                                    name="System Init",
                                    run_type="chain",
                                    inputs={"event": "system_init"},
                                    metadata=child_metadata
                                )
                                child.end(outputs=output.content)
                                
                            elif output.type == "claude_message":
                                child = current_run.create_child(
                                    name="Claude Message",
                                    run_type="llm",
                                    inputs={"type": "reasoning"},
                                    metadata=child_metadata
                                )
                                child.end(outputs={"message": output.content})
                                
                            elif output.type == "tool_call":
                                tool_name = output.content.get('name', 'unknown') if isinstance(output.content, dict) else 'unknown'
                                child = current_run.create_child(
                                    name=f"Tool Use: {tool_name}",
                                    run_type="tool",
                                    inputs=output.content.get('input', {}) if isinstance(output.content, dict) else {},
                                    metadata=child_metadata
                                )
                                child.end(outputs={"tool": tool_name})
                                
                            elif output.type == "tool_result":
                                child = current_run.create_child(
                                    name="Tool Result",
                                    run_type="tool",
                                    inputs={"tool_use_id": output.content.get('tool_use_id') if isinstance(output.content, dict) else None},
                                    metadata=child_metadata
                                )
                                result_content = output.content.get('content', '') if isinstance(output.content, dict) else str(output.content)
                                child.end(outputs={"result": result_content[:500]})  # Truncate large results
                                
                            elif output.type == "result":
                                is_error = output.content.get('is_error', False) if isinstance(output.content, dict) else False
                                child = current_run.create_child(
                                    name="Final Result",
                                    run_type="chain",
                                    inputs={"event": "completion"},
                                    metadata=child_metadata
                                )
                                child.end(outputs={
                                    "success": not is_error,
                                    "result": output.content.get('result', '') if isinstance(output.content, dict) else str(output.content)
                                })
                            
                            elif output.type == "error":
                                child = current_run.create_child(
                                    name="Error",
                                    run_type="chain",
                                    inputs={"event": "error"},
                                    metadata=child_metadata
                                )
                                child.end(outputs={"error": output.content})
                        
                        if on_output:
                            if asyncio.iscoroutinefunction(on_output):
                                await on_output(traced_output)
                            else:
                                on_output(traced_output)
        
        async def handle_stderr(data: str):
            """Handle stderr data from the command."""
            if data.strip():
                logger.warning(f"⚠️ STDERR: {data}")
        
        # Execute with streaming
        result = await sandbox.commands.run(
            full_command,
            on_stdout=handle_stdout,
            on_stderr=handle_stderr,
            cwd=cwd or "/home/<USER>/workspace",
            timeout=timeout
        )
        
        if result.exit_code != 0:
            error_msg = f"Claude command exited with code {result.exit_code}"
            logger.error(f"❌ {error_msg}")
            session.finalize(success=False, error=error_msg)
        else:
            logger.info(f"✅ Claude Code session completed successfully")
            if not session.end_time:  # If not already finalized by result event
                session.finalize(success=True)
        
    except asyncio.TimeoutError:
        error_msg = f"Claude command timed out after {timeout} seconds"
        logger.error(f"⏱️ {error_msg}")
        session.finalize(success=False, error=error_msg)
    except Exception as e:
        error_msg = f"Failed to run Claude: {e}"
        logger.error(f"💥 {error_msg}")
        session.finalize(success=False, error=error_msg)
    
    logger.info(f"📊 Session summary:")
    logger.info(f"   ⏱️ Duration: {session.elapsed_time:.2f}s")
    logger.info(f"   📝 Outputs collected: {len(session.outputs)}")
    if session.total_cost_usd:
        logger.info(f"   💰 Total cost: ${session.total_cost_usd:.4f}")

    return session


async def stream_claude_in_sandbox(
    sandbox: AsyncSandbox,
    prompt: str,
    claude_options: Optional[Dict[str, Any]] = None,
    cwd: Optional[str] = None,
    timeout: int = 300
) -> AsyncIterator[ClaudeOutput]:
    """Stream Claude Code outputs as they arrive.
    
    This is a generator version that yields outputs in real-time.
    
    Args:
        sandbox: The E2B sandbox instance
        prompt: The prompt to send to Claude
        claude_options: Additional options for Claude CLI
        cwd: Working directory for Claude
        timeout: Command timeout in seconds
        
    Yields:
        ClaudeOutput objects as they are parsed from the stream
    """
    outputs_queue = asyncio.Queue()
    session = ClaudeSession(
        session_id=f"e2b-{sandbox.sandbox_id}-{int(datetime.now().timestamp())}",
        prompt=prompt
    )
    
    async def output_callback(output: ClaudeOutput):
        await outputs_queue.put(output)
    
    # Run Claude in a background task
    async def run_claude_task():
        try:
            await run_claude_in_sandbox(
                sandbox=sandbox,
                prompt=prompt,
                claude_options=claude_options,
                on_output=output_callback,
                cwd=cwd,
                timeout=timeout
            )
        finally:
            # Signal completion
            await outputs_queue.put(None)
    
    # Start the Claude task
    task = asyncio.create_task(run_claude_task())
    
    try:
        # Yield outputs as they arrive
        while True:
            output = await outputs_queue.get()
            if output is None:  # End signal
                break
            yield output
    finally:
        # Ensure task is complete
        await task
