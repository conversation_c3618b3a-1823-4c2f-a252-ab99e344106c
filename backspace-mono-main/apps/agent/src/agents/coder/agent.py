"""
Coder Agent - A comprehensive code analysis and modification tool.
"""

import asyncio
from typing import Optional, Literal
from langchain_core.messages import AIMessage, ToolMessage, HumanMessage
from agents.base import BaseAgent
from agents.coder.graph import CoderGraph
from agents.tools import local_tools, read_tools
from langchain_core.runnables import RunnableConfig
from agents.llm_config import get_thinking_llm  # Import the thinking-enabled LLM


class CoderAgent(BaseAgent):
    """
    Coder agent for code analysis and modification with enhanced thinking visibility.
    
    This agent performs comprehensive code analysis including:
    - Code structure analysis
    - File modifications and creation
    - Git operations
    - Issue tracking integration
    - Enhanced internal reasoning visibility
    """
    
    def __init__(
        self, 
        *,
        model_provider: Optional[Literal["anthropic", "openai"]] = "anthropic",
        model_name: Optional[str] = "claude-3-5-sonnet-20241022",
        use_sandbox: bool = True,
        enable_thinking: bool = True,
    ):
        """
        Initialize CoderAgent with enhanced thinking capabilities.
        
        Args:
            model_provider: Model provider ("anthropic" or "openai")
            model_name: Specific model name
            use_sandbox: Whether to use sandbox for code execution
            enable_thinking: En<PERSON>'s thinking mode for detailed reasoning visibility
        """
        # Use thinking-enabled LLM for maximum observability
        if enable_thinking and model_provider == "anthropic":
            self.llm = get_thinking_llm(model_provider, model_name)
        else:
            # Fallback to standard LLM
            from agents.llm_config import get_llm
            self.llm = get_llm(model_provider, model_name, enable_thinking=enable_thinking)
        
        self.use_sandbox = use_sandbox
        self.sandbox = None

        # Include both sandbox tools and local tools for coder agent
        base_tools = read_tools if use_sandbox else local_tools
        self.tools = base_tools
        self.graph = CoderGraph(llm=self.llm, tools=self.tools, use_sandbox=use_sandbox).compile()

    
    async def _execute(self, query: str, config: Optional[RunnableConfig], **kwargs) -> str:
        """
        Execute the CoderAgent logic using the coder graph with enhanced thinking visibility.
        
        Args:
            query: The coding task or analysis request
            config: Optional configuration for the agent
            **kwargs: Additional parameters
            
        Returns:
            The final analysis or modification result from the assistant
        """
        
        # Stream and pretty print with enhanced thinking details
        last_chunk = None
        async for chunk in self.graph.astream({
            "query": query,
            "messages": [],
            "iterations": 0,
            "repo_id": kwargs.get("repo_id")
        }, config=config):
            # Pretty print the messages as they come
            if "reasoner" in chunk:
                for msg in chunk["reasoner"].get("messages", []):
                    if isinstance(msg, (AIMessage, HumanMessage)) and msg.content:
                        print(msg.pretty_print())
                        
                        # Enhanced thinking output
                        if hasattr(msg, 'thinking') and msg.thinking:
                            print(f"🧠 Claude's Internal Thinking:")
                            print(f"   {msg.thinking}")
                        
            elif "tools" in chunk:
                for msg in chunk["tools"].get("messages", []):
                    if isinstance(msg, ToolMessage):
                        print(msg.pretty_print())
            
            last_chunk = chunk
        
        if last_chunk is None:
            raise ValueError("No response was generated from the agent")
        
        # Extract the final AI message from the state
        messages = last_chunk["reasoner"].get("messages", [])
        return messages[-1].content


# Example usage
if __name__ == "__main__":
    async def main():
        # Example 1: Initialize coder agent with thinking mode
        coder_agent = CoderAgent(
            model_provider="anthropic", 
            model_name="claude-3-5-sonnet-20241022",
            enable_thinking=True
        )
        
        # Example 2: Code analysis with visible thinking
        response = await coder_agent.run("Analyze the codebase structure and suggest improvements")
        print(f"\nFinal Analysis: {response}")
        
        # Example 3: File modification with thinking visibility
        response = await coder_agent.run("Create a new utility function for data validation")
        print(f"\nModification Result: {response}")
    
    # Run the async main function
    asyncio.run(main())