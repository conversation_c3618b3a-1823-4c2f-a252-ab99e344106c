from typing import Any
from langchain_core.language_models.chat_models import BaseChatModel
from langgraph.graph.state import CompiledGraph, StateGraph
from langgraph.graph import END, START
from langchain_core.runnables.config import RunnableConfig
from langchain_core.tools import BaseTool
from langchain_core.messages import HumanMessage

from agents.base import BaseGraph
from agents.branch.states import BranchState
from agents.scanner.agent import ScannerAgent
from agents.coder.agent import CoderAgent
from agents.branch.prompts import get_scanner_query, get_coder_query

# Add LangSmith observability imports
from langsmith import traceable, get_current_run_tree

import logging

logger = logging.getLogger(__name__)


class BranchGraph(BaseGraph):
    """Branch agent that combines scanner and coder graphs using subgraphs."""
    
    def __init__(self, llm: BaseChatModel, tools: list[BaseTool]):
        super().__init__(llm=llm, tools=tools)
        
        # Initialize subgraphs
        self.scanner_graph = ScannerAgent().graph
        self.coder_graph = CoderAgent().graph

    # =================================== NODES ====================================

    @traceable(
        name="Branch Scanner Subgraph",
        run_type="chain",
        metadata={"agent": "branch", "subgraph": "scanner"},
        tags=["branch", "scanner", "orchestration"]
    )
    async def scanner_subgraph(self, state: BranchState, config: RunnableConfig) -> dict[str, Any]:
        """Execute the scanner subgraph and extract results."""
        current_run = get_current_run_tree()
        
        logger.info("🔍 Executing scanner subgraph...")
        
        # Auto-generate query using Jinja2 templates
        scan_type = state["scan_type"]
        testing = state.get("testing", False)
        
        # Get scan type string for template
        scan_type_str = scan_type.value if hasattr(scan_type, 'value') else str(scan_type)
        
        # Generate query using Jinja2 template
        query = get_scanner_query(scan_type_str, testing)
        
        # Log orchestration context
        if current_run:
            current_run.add_metadata({
                "scan_type": scan_type_str,
                "testing_mode": testing,
                "repo_id": state.get("repo_id"),
                "generated_query": query[:100] + "..." if len(query) > 100 else query
            })
        
        # Prepare scanner input
        scanner_input = {
            "query": query,
            "scan_type": scan_type,
            "repo_id": state.get("repo_id"),
            "messages": []
        }
        
        # Execute scanner graph
        scanner_result = await self.scanner_graph.ainvoke(scanner_input, config)
        
        # Extract the last message content as scan results
        scan_results = ""
        if scanner_result.get("messages"):
            last_message = scanner_result["messages"][-1]
            if hasattr(last_message, 'content'):
                scan_results = last_message.content
            else:
                scan_results = str(last_message)
        
        # Log scanner execution results
        if current_run:
            current_run.add_metadata({
                "scanner_success": bool(scan_results),
                "results_length": len(scan_results),
                "message_count": len(scanner_result.get("messages", [])),
                "results_preview": scan_results[:200] + "..." if len(scan_results) > 200 else scan_results
            })
        
        logger.info("✅ Scanner subgraph completed")
        
        return {
            "scan_results": scan_results,
            "messages": [HumanMessage(content=f"Scanner analysis completed: {scan_results}")]
        }

    @traceable(
        name="Branch Coder Subgraph",
        run_type="chain",
        metadata={"agent": "branch", "subgraph": "coder"},
        tags=["branch", "coder", "orchestration"]
    )
    async def coder_subgraph(self, state: BranchState, config: RunnableConfig) -> dict[str, Any]:
        """Execute the coder subgraph with scanner results."""
        current_run = get_current_run_tree()
        
        logger.info("🔧 Executing coder subgraph with scanner results...")
        
        # Create coder query using Jinja2 template
        scan_results = state.get("scan_results", "")
        testing = state.get("testing", False)
        
        # Generate coder query using Jinja2 template
        coder_query = get_coder_query(scan_results, testing)
        
        # Log orchestration context
        if current_run:
            current_run.add_metadata({
                "scan_results_length": len(scan_results),
                "testing_mode": testing,
                "repo_id": state.get("repo_id"),
                "db_issue_id": state.get("db_issue_id"),
                "generated_query": coder_query[:100] + "..." if len(coder_query) > 100 else coder_query,
                "scan_results_preview": scan_results[:200] + "..." if len(scan_results) > 200 else scan_results
            })
        
        # Prepare coder input
        coder_input = {
            "query": coder_query,
            "repo_id": state.get("repo_id"),
            "db_issue_id": state.get("db_issue_id"),
            "messages": []
        }
        
        # Execute coder graph
        coder_result = await self.coder_graph.ainvoke(coder_input, config)
        
        # Log coder execution results
        if current_run:
            current_run.add_metadata({
                "coder_success": bool(coder_result.get("messages")),
                "coder_message_count": len(coder_result.get("messages", [])),
                "final_result_preview": str(coder_result.get("messages", [])[-1])[:200] + "..." if coder_result.get("messages") else "No messages"
            })
        
        logger.info("✅ Coder subgraph completed")
        
        # Return the coder results
        return {
            "messages": coder_result.get("messages", [])
        }

    # =================================== COMPILATION ====================================

    def compile(self) -> CompiledGraph:
        """Compile the branch graph with scanner -> coder flow."""
        builder = StateGraph(BranchState)

        # Add subgraph nodes
        builder.add_node("scanner", self.scanner_subgraph, retry=self.retry_policy)
        builder.add_node("coder", self.coder_subgraph, retry=self.retry_policy)

        # Define the flow: START -> scanner -> coder -> END
        builder.add_edge(START, "scanner")
        builder.add_edge("scanner", "coder")
        builder.add_edge("coder", END)

        return builder.compile()