"""LLM configuration utilities for selecting and instantiating language models."""

from typing import Literal
from dataclasses import dataclass
from langchain_anthropic import <PERSON><PERSON><PERSON><PERSON><PERSON>
from langchain_openai import ChatOpenAI
from langchain_core.language_models.chat_models import BaseChatModel


def get_llm(model_provider: Literal["anthropic", "openai"], model_name: str, enable_thinking: bool = True) -> BaseChatModel:
    """
    Get a configured language model instance.
    
    Args:
        model_provider: Either "anthropic" or "openai"
        model_name: The specific model name (see supported models below)
        enable_thinking: Enable <PERSON>'s thinking mode for detailed internal reasoning (Anthropic only)
        
    Returns:
        Configured ChatAnthropic or ChatOpenAI instance
        
    """
    if model_provider == "anthropic":
        # Note: Thinking mode is only available on specific Claude models
        # For now, we'll use standard configuration with enhanced prompting
        return ChatAnthropic(
            model=model_name,
            max_tokens=8192,
            temperature=0,
            max_retries=10
        )
    elif model_provider == "openai":
        # o1 models don't support temperature parameter
        if model_name.startswith(("o1", "o3", "o4")):
            return ChatOpenAI(
                model=model_name,
                max_tokens=8192,
                max_retries=10
            )
        else:
            return ChatOpenAI(
                model=model_name,
                max_tokens=8192,
                temperature=0,
                max_retries=10
            )


def get_thinking_llm(model_provider: Literal["anthropic", "openai"] = "anthropic", 
                    model_name: str = "claude-3-5-sonnet-20241022") -> BaseChatModel:
    """
    Get an LLM configured for enhanced reasoning visibility.
    
    Note: True thinking mode is only available on specific Claude models.
    This function provides enhanced reasoning through better prompting.
    
    Returns:
        ChatAnthropic configured for detailed reasoning
    """
    if model_provider == "anthropic":
        # Use standard Claude with enhanced reasoning prompts
        return ChatAnthropic(
            model=model_name,
            max_tokens=8192,
            temperature=0.1,  # Slightly higher for more creative reasoning
            max_retries=10
        )
    else:
        # Fallback to regular LLM for non-Anthropic providers
        return get_llm(model_provider, model_name, enable_thinking=False)
