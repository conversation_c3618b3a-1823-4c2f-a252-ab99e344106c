#!/usr/bin/env python3
"""
Test your Coder Agent observability implementation
This will generate traces you can see in LangSmith dashboard
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Ensure environment is set up for LangSmith
from dotenv import load_dotenv
load_dotenv()

# Verify LangSmith configuration
os.environ["LANGSMITH_TRACING"] = "true"
os.environ["LANGSMITH_PROJECT"] = "pr-flowery-gastropod-81"

async def test_coder_agent_observability():
    """Test the Coder Agent with your enhanced observability"""
    
    print("🚀 Testing Coder Agent Observability")
    print("=" * 60)
    print("📊 This will generate traces in your LangSmith dashboard")
    print("🎯 Project: pr-flowery-gastropod-81")
    print("")
    
    try:
        # Import your enhanced coder agent
        from agents.coder.agent import CoderAgent
        
        print("✅ Successfully imported CoderAgent")
        
        # Initialize with your configuration
        print("🔧 Initializing Coder Agent...")
        coder_agent = CoderAgent(
            model_provider="anthropic",
            model_name="claude-3-5-haiku-20241022",
            use_sandbox=True
        )
        
        print(f"✅ Coder Agent initialized:")
        print(f"   Model: claude-3-5-haiku-20241022")
        print(f"   Tools available: {len(coder_agent.tools)}")
        print(f"   Sandbox enabled: True")
        
        # Test query that will trigger reasoning
        test_query = "create a simple hello world python function"
        
        print(f"\n📝 Running test query: '{test_query}'")
        print("⏳ This will generate observable traces...")
        print("🔍 Watch for traces in LangSmith!")
        
        # Run the agent - this will create traces
        result = await coder_agent.run(test_query)
        
        print("\n✅ Coder Agent execution completed!")
        print(f"📊 Result preview: {str(result)[:300]}...")
        
        print("\n" + "=" * 60)
        print("🎯 CHECK LANGSMITH NOW!")
        print("=" * 60)
        print("1. Go to: https://smith.langchain.com/")
        print("2. Project: pr-flowery-gastropod-81")
        print("3. Look for the NEWEST trace at the top")
        print("")
        print("✅ You should see these traces from YOUR implementation:")
        print("   🧠 'Coder Reasoner' - Shows Claude's reasoning process")
        print("   🔧 'Coder Tools' - Shows tool execution details")
        print("   🎯 'Should Continue Decision' - Shows routing logic")
        print("")
        print("📋 In each trace, check these tabs:")
        print("   📥 INPUT: Full system prompt + your query")
        print("   📤 OUTPUT: Claude's response with tool calls")
        print("   📊 METADATA: Your enhanced metadata (agent_type, message_count, etc.)")
        print("")
        print("🚀 This proves your reasoning observability is working!")
        print("=" * 60)
        
        return {
            "success": True,
            "traces_generated": True,
            "reasoning_observable": True
        }
        
    except Exception as e:
        print(f"\n❌ Error during test: {e}")
        print(f"Error type: {type(e).__name__}")
        
        # Check for common issues
        if "credit balance is too low" in str(e):
            print("\n💡 API Credit Issue:")
            print("   - This is expected if Anthropic credits are low")
            print("   - But LangSmith traces should still be generated!")
            print("   - Check LangSmith for partial traces")
        elif "Database client not initialized" in str(e):
            print("\n💡 Database Issue:")
            print("   - This is expected in test environment")
            print("   - The observability part should still work")
            print("   - Check LangSmith for partial traces")
        else:
            print("\n🔧 Possible issues:")
            print("   - Check .env file has correct API keys")
            print("   - Verify LangSmith configuration")
            print("   - Check network connectivity")
        
        print(f"\n📊 Even with errors, check LangSmith for any traces that were created!")
        
        return {
            "success": False,
            "error": str(e),
            "check_langsmith": True
        }

async def main():
    """Main test function"""
    
    print("🔍 CODER AGENT OBSERVABILITY TEST")
    print("=" * 60)
    print("🎯 Goal: Verify your reasoning traces are visible in LangSmith")
    print("🎯 Focus: Testing the @traceable decorators you implemented")
    print("")
    
    # Show configuration
    print("📋 LangSmith Configuration:")
    print(f"   LANGSMITH_TRACING: {os.getenv('LANGSMITH_TRACING', 'Not set')}")
    print(f"   LANGSMITH_PROJECT: {os.getenv('LANGSMITH_PROJECT', 'Not set')}")
    print(f"   ANTHROPIC_API_KEY: {'Set' if os.getenv('ANTHROPIC_API_KEY') else 'Not set'}")
    print("")
    
    # Run the test
    result = await test_coder_agent_observability()
    
    print(f"\n🏁 Test completed with result: {result}")

if __name__ == "__main__":
    asyncio.run(main())