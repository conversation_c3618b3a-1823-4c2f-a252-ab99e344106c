"""Test script to verify <PERSON>'s thinking mode is working"""

import asyncio
import os
from agents.coder.deployment import agent
from db import db_manager

async def test_thinking_mode():
    """Test that <PERSON>'s thinking mode provides detailed internal reasoning"""
    
    # Connect to database
    print("🔌 Connecting to database...")
    await db_manager.connect()
    print("✅ Database connected")
    
    # Verify LangSmith is configured
    print("\n📊 LangSmith Configuration:")
    print(f"  - LANGSMITH_TRACING: {os.getenv('LANGSMITH_TRACING', 'not set')}")
    print(f"  - LANGSMITH_PROJECT: {os.getenv('LANGSMITH_PROJECT', 'not set')}")
    print(f"  - LANGSMITH_API_KEY: {'set' if os.getenv('LANGSMITH_API_KEY') else 'not set'}")
    
    # Test query that requires reasoning
    test_query = "analyze the current codebase structure and suggest one improvement"
    print(f"\n🧪 Testing thinking mode with query: '{test_query}'")
    print("📍 Watch for <PERSON>'s internal thinking process!")
    print(f"   <PERSON><PERSON>mith: https://smith.langchain.com/o/c0b849de-4609-57f1-8c02-493a30cddf03/projects/p/{os.getenv('LANGSMITH_PROJECT', '')}")
    
    # Run the agent with enhanced thinking visibility
    print("\n🧠 Running agent with thinking mode enabled:")
    print("=" * 60)
    
    initial_state = {
        "query": test_query,
        "messages": [],
        "iterations": 0,
        "sandbox": None
    }
    
    try:
        thinking_steps = []
        reasoning_steps = []
        
        async for chunk in agent.astream(initial_state):
            for node_name, node_output in chunk.items():
                print(f"\n📍 Node: {node_name}")
                
                if messages := node_output.get("messages"):
                    for msg in messages:
                        if hasattr(msg, 'content') and msg.content:
                            # Check for thinking content
                            if hasattr(msg, 'thinking') and msg.thinking:
                                thinking_steps.append(msg.thinking)
                                print(f"   🧠 THINKING: {msg.thinking[:200]}...")
                            
                            # Regular content
                            reasoning_steps.append(msg.content)
                            print(f"   💭 REASONING: {str(msg.content)[:200]}...")
                            
                        if hasattr(msg, 'tool_calls') and msg.tool_calls:
                            print(f"   🔧 TOOL CALLS: {[tc.get('name', tc) for tc in msg.tool_calls]}")
                
                if "error" in node_output:
                    print(f"   ❌ Error: {node_output['error']}")
        
        print("\n" + "=" * 60)
        print("📊 THINKING MODE ANALYSIS:")
        print(f"   🧠 Thinking steps captured: {len(thinking_steps)}")
        print(f"   💭 Reasoning steps captured: {len(reasoning_steps)}")
        
        if thinking_steps:
            print("\n🎯 SUCCESS: Claude's internal thinking is visible!")
            print("   Sample thinking process:")
            for i, thinking in enumerate(thinking_steps[:2], 1):
                print(f"   {i}. {thinking[:100]}...")
        else:
            print("\n⚠️  No thinking steps captured - may need API key update or model adjustment")
        
        print("\n✅ Thinking mode test completed!")
        
    except Exception as e:
        print(f"\n❌ Error during thinking test: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Cleanup
        await db_manager.disconnect()
        print("\n🔌 Database disconnected")

if __name__ == "__main__":
    asyncio.run(test_thinking_mode()) 